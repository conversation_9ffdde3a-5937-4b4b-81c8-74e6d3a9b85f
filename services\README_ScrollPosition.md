# 滚动位置管理服务使用指南

## 📋 概述

`scrollPositionService` 是一个可复用的滚动位置记录和恢复服务，专为微信小程序设计。它提供了最小侵入性的集成方式，支持多页面、多tab的滚动位置管理，并具备自动持久化功能。

## ✨ 功能特性

- **多页面支持**：每个页面独立管理滚动位置，互不干扰
- **多tab支持**：支持tab页面的独立滚动位置记录
- **自动持久化**：滚动位置自动保存到本地缓存，页面重新加载后可恢复
- **最小侵入性**：只需几行代码即可集成到现有页面
- **兼容现有逻辑**：不影响页面现有的滚动处理逻辑
- **灵活配置**：支持缓存开关、过期时间等配置

## 🚀 快速开始

### 1. 基础页面集成（无tab）

```javascript
// 1. 导入服务
const { 
  initScrollPositionManager, 
  recordScrollPosition, 
  restoreScrollPosition 
} = require("@/services/scrollPositionService")

Page({
  async onLoad(options) {
    // 2. 初始化滚动位置管理器
    initScrollPositionManager(this, "job_list")
    
    // 其他初始化代码...
  },

  onPageScroll(e) {
    // 3. 记录滚动位置
    recordScrollPosition(this, e.scrollTop)
    
    // 其他滚动处理逻辑...
  },

  onShow() {
    // 4. 恢复滚动位置（可选）
    setTimeout(() => {
      restoreScrollPosition(this)
    }, 100) // 延迟确保页面渲染完成
  }
})
```

### 2. Tab页面集成

```javascript
const { 
  initScrollPositionManager, 
  recordScrollPosition, 
  restoreScrollPosition 
} = require("@/services/scrollPositionService")

Page({
  data: {
    activeIndex: 0,
  },

  async onLoad(options) {
    // 初始化多tab滚动位置管理
    initScrollPositionManager(this, "home", {
      tabs: { 0: 0, 1: 0 }, // 公告tab和考试动态tab
      enableCache: true,
      cacheExpireTime: 24 * 60 * 60 * 1000, // 24小时
    })
  },

  onPageScroll(e) {
    const { activeIndex } = this.data
    // 记录当前tab的滚动位置
    recordScrollPosition(this, e.scrollTop, activeIndex)
    
    // 其他滚动处理逻辑...
  },

  // tab切换时恢复滚动位置
  changeTab(e) {
    const newIndex = e.currentTarget.dataset.index
    this.setData({ activeIndex: newIndex })
    
    // 恢复新tab的滚动位置
    setTimeout(() => {
      restoreScrollPosition(this, newIndex)
    }, 100)
  }
})
```

## 📚 API 参考

### initScrollPositionManager(pageInstance, pageKey, options)

初始化滚动位置管理器

**参数：**
- `pageInstance` (Object): 页面实例，通常传入 `this`
- `pageKey` (string): 页面唯一标识，如 `'home'`, `'job_list'`, `'my_home'`
- `options` (Object): 配置选项
  - `tabs` (Array|Object): tab配置，支持数组或对象格式
  - `enableCache` (boolean): 是否启用缓存，默认 `true`
  - `cacheExpireTime` (number): 缓存过期时间（毫秒），默认24小时

**示例：**
```javascript
// 基础页面
initScrollPositionManager(this, "job_list")

// 多tab页面
initScrollPositionManager(this, "home", {
  tabs: { 0: 0, 1: 0, "2-0": 0, "2-1": 0 },
  enableCache: true,
  cacheExpireTime: 12 * 60 * 60 * 1000, // 12小时
})
```

### recordScrollPosition(pageInstance, scrollTop, tabKey)

记录滚动位置

**参数：**
- `pageInstance` (Object): 页面实例
- `scrollTop` (number): 滚动位置
- `tabKey` (string|number): tab标识，可选，默认 `"page"`

### restoreScrollPosition(pageInstance, tabKey, options)

恢复滚动位置

**参数：**
- `pageInstance` (Object): 页面实例
- `tabKey` (string|number): tab标识，可选，默认 `"page"`
- `options` (Object): 恢复选项
  - `duration` (number): 动画时长，默认0
  - `force` (boolean): 是否强制恢复，默认 `false`

## 🔧 现有页面改造示例

### 改造前（home/index.js）

```javascript
Page({
  async onLoad(options) {
    // 初始化Tab滚动位置记忆功能
    this.tabScrollPositions = {
      0: 0, // 公告tab的滚动位置
      1: 0, // 考试动态tab的滚动位置
    }
    // 其他代码...
  },

  onPageScroll(e) {
    const scrollTop = e.scrollTop
    const { activeIndex } = this.data
    this.tabScrollPositions[activeIndex] = scrollTop
    // 其他代码...
  },

  restoreTabScrollPosition(tabIndex) {
    const scrollTop = this.tabScrollPositions[tabIndex] || 0
    wx.pageScrollTo({
      scrollTop: scrollTop,
      duration: 0,
    })
  },
})
```

### 改造后

```javascript
const { 
  initScrollPositionManager, 
  recordScrollPosition, 
  restoreScrollPosition 
} = require("@/services/scrollPositionService")

Page({
  async onLoad(options) {
    // 使用滚动位置管理服务
    initScrollPositionManager(this, "home", {
      tabs: { 0: 0, 1: 0 },
    })
    // 其他代码...
  },

  onPageScroll(e) {
    const { activeIndex } = this.data
    recordScrollPosition(this, e.scrollTop, activeIndex)
    // 其他代码...
  },

  // 简化的恢复方法
  restoreTabScrollPosition(tabIndex) {
    restoreScrollPosition(this, tabIndex)
  },
})
```

## 💡 最佳实践

### 1. 页面标识命名规范

建议使用以下命名规范：
- 主页面：`'home'`, `'job_list'`, `'my_home'`
- 详情页面：`'job_detail'`, `'notice_detail'`
- 列表页面：`'notice_list'`, `'exam_list'`

### 2. 恢复时机

```javascript
onShow() {
  // 延迟恢复，确保页面渲染完成
  setTimeout(() => {
    restoreScrollPosition(this)
  }, 100)
},

// tab切换时立即恢复
changeTab(e) {
  const newIndex = e.currentTarget.dataset.index
  this.setData({ activeIndex: newIndex })
  
  // 可以不延迟，因为页面已经渲染
  restoreScrollPosition(this, newIndex)
}
```

### 3. 缓存管理

```javascript
// 清除特定页面的滚动位置缓存
const { clearScrollPositionCache } = require("@/services/scrollPositionService")
clearScrollPositionCache("home")

// 清除当前页面的滚动位置
const { clearScrollPosition } = require("@/services/scrollPositionService")
clearScrollPosition(this) // 清除所有tab
clearScrollPosition(this, 0) // 清除指定tab
```

## ⚠️ 注意事项

1. **初始化时机**：必须在 `onLoad` 中调用 `initScrollPositionManager`
2. **恢复时机**：建议在 `onShow` 或tab切换时恢复，并适当延迟
3. **页面标识**：确保每个页面使用唯一的 `pageKey`
4. **兼容性**：服务不会影响现有的滚动处理逻辑
5. **性能**：滚动位置会自动保存到缓存，频繁滚动不会影响性能

## 🔄 迁移指南

对于现有页面，建议按以下步骤迁移：

1. **保留现有逻辑**：先不删除现有的滚动位置处理代码
2. **添加服务**：在现有代码基础上添加滚动位置服务
3. **测试验证**：确保功能正常后再移除旧代码
4. **逐步迁移**：一个页面一个页面地进行迁移

这样可以确保迁移过程的安全性和稳定性。
