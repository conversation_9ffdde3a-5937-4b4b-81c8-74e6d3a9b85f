/**
 * Home页面滚动位置服务集成示例
 * 
 * 这个文件展示了如何在现有的 home/index.js 页面中集成滚动位置管理服务
 * 
 * 集成步骤：
 * 1. 导入滚动位置服务
 * 2. 在 onLoad 中初始化服务
 * 3. 在 onPageScroll 中记录滚动位置
 * 4. 在 tab 切换时恢复滚动位置
 * 5. 可选：在 onShow 中恢复滚动位置
 */

// ===== 第1步：导入服务 =====
const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
const { handleMultiSelect } = require("@/services/selectionService")
const { getSelectedRegionsCache } = require("@/utils/cache/regionCache")
const {
  getNoticeSelectForTemplateCache,
  setExamSelectForTemplateCache,
  getExamSelectForTemplateCache,
  setNoticeSelectForTemplateCache,
} = require("@/utils/cache/filterCache")

const PopupMenuFilterMixin = require("@/components/popup/popup-menu-filter/mixin")
const { processMenuList } = require("@/services/menuServices")

// 导入滚动位置管理服务
const { 
  initScrollPositionManager, 
  recordScrollPosition, 
  restoreScrollPosition 
} = require("@/services/scrollPositionService")

const pageConfig = {
  data: {
    // 现有的 data 配置...
    activeIndex: 0,
    // 其他数据...
  },

  // ===== 第2步：在 onLoad 中初始化服务 =====
  async onLoad(options) {
    console.log("页面加载，初始化数据")
    await APP.checkLoadRequest()

    // 使用新的滚动位置管理服务替代原有的 tabScrollPositions
    initScrollPositionManager(this, "home", {
      tabs: { 0: 0, 1: 0 }, // 公告tab和考试动态tab
      enableCache: true,
      cacheExpireTime: 24 * 60 * 60 * 1000, // 24小时缓存
    })

    // 保留原有的初始化逻辑
    this.initPopupMenuFilterMixin()
    await this.getHome()
    this.setMenuStickyTop()
    this.restoreFilterFromCache()

    const apiParams = this.buildApiParams(this.data.noticeSelectForTemplate)
    await this.getArticleList(apiParams, false)
  },

  // ===== 第3步：在 onShow 中恢复滚动位置（可选） =====
  async onShow() {
    console.log("页面显示，更新筛选条件")
    if (!this.data.isPageLoadComplete) {
      return
    }

    await this.getHome()
    this.setMenuStickyTop()
    this.restoreFilterFromCache()

    const apiParams = this.buildApiParams(this.data.noticeSelectForTemplate)
    await this.getArticleList(apiParams, false)

    // 恢复当前tab的滚动位置
    setTimeout(() => {
      const { activeIndex } = this.data
      restoreScrollPosition(this, activeIndex)
    }, 100) // 延迟确保页面渲染完成
  },

  // ===== 第4步：在 onPageScroll 中记录滚动位置 =====
  onPageScroll(e) {
    // 如果页面滚动被禁用，则不处理滚动事件
    if (this.isPageScrollDisabled && this.isPageScrollDisabled()) {
      return
    }

    const scrollTop = e.scrollTop
    const { headerHeight, activeIndex } = this.data

    // 使用新的滚动位置服务记录滚动位置
    recordScrollPosition(this, scrollTop, activeIndex)

    // 保留原有的滚动处理逻辑
    if (activeIndex == 0) {
      this.setNavigationBarStyle()
    }

    const { menuOffsetTop } = this.data
    const isMenuSticky =
      menuOffsetTop > 0 && scrollTop >= menuOffsetTop - headerHeight

    if (isMenuSticky !== this.data.menuSticky) {
      this.setData({
        menuSticky: isMenuSticky,
      })
    }
  },

  // ===== 第5步：简化 restoreTabScrollPosition 方法 =====
  /**
   * 恢复指定tab的滚动位置
   * @param {number} tabIndex tab索引
   */
  restoreTabScrollPosition(tabIndex) {
    // 使用新的滚动位置服务
    restoreScrollPosition(this, tabIndex)
  },

  // ===== 第6步：在 tab 切换时恢复滚动位置 =====
  /**
   * 切换tab（示例方法，根据实际的tab切换方法调整）
   */
  changeTab(e) {
    const newIndex = e.currentTarget.dataset.index
    if (newIndex === this.data.activeIndex) {
      return
    }

    this.setData({ activeIndex: newIndex })
    
    // 切换tab后恢复对应的滚动位置
    setTimeout(() => {
      restoreScrollPosition(this, newIndex)
    }, 50) // 短延迟确保tab切换完成
    
    // 其他tab切换逻辑...
  },

  // 其他现有方法保持不变...
  onShareAppMessage() {},
}

// 创建页面实例
Page(pageConfig)

/**
 * 集成说明：
 * 
 * 1. 兼容性：新服务完全兼容现有代码，不会影响其他功能
 * 
 * 2. 渐进式迁移：
 *    - 可以先保留原有的 this.tabScrollPositions 代码
 *    - 同时添加新的滚动位置服务
 *    - 测试无误后再移除旧代码
 * 
 * 3. 性能优化：
 *    - 滚动位置自动缓存，页面重新加载后可恢复
 *    - 缓存有过期时间，避免数据过期
 *    - 记录操作经过优化，不会影响滚动性能
 * 
 * 4. 调试支持：
 *    - 服务提供详细的控制台日志
 *    - 可以通过日志查看滚动位置的记录和恢复情况
 * 
 * 5. 扩展性：
 *    - 支持更多tab的添加
 *    - 支持自定义缓存策略
 *    - 支持清除特定tab的滚动位置
 */

/**
 * 实际集成时的修改点：
 * 
 * 在现有的 pages/home/<USER>/index.js 文件中：
 * 
 * 1. 在文件顶部添加服务导入：
 *    const { initScrollPositionManager, recordScrollPosition, restoreScrollPosition } = require("@/services/scrollPositionService")
 * 
 * 2. 在 onLoad 方法中替换：
 *    // 原代码：
 *    this.tabScrollPositions = { 0: 0, 1: 0 }
 *    
 *    // 新代码：
 *    initScrollPositionManager(this, "home", { tabs: { 0: 0, 1: 0 } })
 * 
 * 3. 在 onPageScroll 方法中替换：
 *    // 原代码：
 *    this.tabScrollPositions[activeIndex] = scrollTop
 *    
 *    // 新代码：
 *    recordScrollPosition(this, scrollTop, activeIndex)
 * 
 * 4. 在 restoreTabScrollPosition 方法中替换：
 *    // 原代码：
 *    const scrollTop = this.tabScrollPositions[tabIndex] || 0
 *    wx.pageScrollTo({ scrollTop: scrollTop, duration: 0 })
 *    
 *    // 新代码：
 *    restoreScrollPosition(this, tabIndex)
 * 
 * 5. 可选：在 onShow 中添加滚动位置恢复（如果需要页面重新显示时恢复滚动位置）
 */
