const { setCache, getCache } = require("@/utils/cache/core/core")

/**
 * 滚动位置管理服务
 * 提供页面滚动位置的记录、恢复和持久化功能
 *
 * 功能特性：
 * - 支持多页面独立管理滚动位置
 * - 支持tab页面的滚动位置记录
 * - 自动持久化到本地缓存
 * - 最小侵入性集成
 * - 兼容现有页面逻辑
 */

const CACHE_KEY_PREFIX = "scroll_position"

/**
 * 初始化滚动位置管理
 * @param {Object} pageInstance 页面实例 (this)
 * @param {string} pageKey 页面唯一标识（如：'home', 'job_list', 'my_home'）
 * @param {Object} options 配置选项
 * @param {Array|Object} options.tabs tab配置，支持数组或对象格式
 * @param {boolean} options.enableCache 是否启用缓存持久化，默认true
 * @param {number} options.cacheExpireTime 缓存过期时间（毫秒），默认24小时
 */
function initScrollPositionManager(pageInstance, pageKey, options = {}) {
  const {
    tabs = {},
    enableCache = true,
    cacheExpireTime = 24 * 60 * 60 * 1000, // 24小时
  } = options

  // 初始化滚动位置存储对象
  pageInstance.scrollPositionManager = {
    pageKey,
    enableCache,
    cacheExpireTime,
    positions: {},
  }

  // 初始化tab滚动位置
  if (Array.isArray(tabs)) {
    // 数组格式：[0, 1, 2] 或 ['tab1', 'tab2']
    tabs.forEach((tabKey) => {
      pageInstance.scrollPositionManager.positions[tabKey] = 0
    })
  } else if (typeof tabs === "object") {
    // 对象格式：{ 0: 0, 1: 0, "2-0": 0 }
    Object.keys(tabs).forEach((tabKey) => {
      pageInstance.scrollPositionManager.positions[tabKey] = tabs[tabKey] || 0
    })
  }

  // 从缓存恢复滚动位置
  if (enableCache) {
    restoreScrollPositionsFromCache(pageInstance)
  }

  console.log(
    `滚动位置管理器初始化完成 - 页面: ${pageKey}`,
    pageInstance.scrollPositionManager.positions
  )
}

/**
 * 记录滚动位置
 * @param {Object} pageInstance 页面实例
 * @param {number} scrollTop 滚动位置
 * @param {string|number} tabKey tab标识，可选，不传则记录为页面级别滚动位置
 */
function recordScrollPosition(pageInstance, scrollTop, tabKey = "page") {
  if (!pageInstance.scrollPositionManager) {
    console.warn("滚动位置管理器未初始化，请先调用 initScrollPositionManager")
    return
  }

  // 记录滚动位置
  pageInstance.scrollPositionManager.positions[tabKey] = scrollTop

  // 自动保存到缓存
  if (pageInstance.scrollPositionManager.enableCache) {
    saveScrollPositionsToCache(pageInstance)
  }
}

/**
 * 恢复滚动位置
 * @param {Object} pageInstance 页面实例
 * @param {string|number} tabKey tab标识，可选，不传则恢复页面级别滚动位置
 * @param {Object} options 恢复选项
 * @param {number} options.duration 动画时长，默认0（无动画）
 * @param {boolean} options.force 是否强制恢复，即使位置为0也恢复
 */
function restoreScrollPosition(pageInstance, tabKey = "page", options = {}) {
  if (!pageInstance.scrollPositionManager) {
    console.warn("滚动位置管理器未初始化，请先调用 initScrollPositionManager")
    return
  }

  const { duration = 0, force = false } = options
  const savedScrollTop =
    pageInstance.scrollPositionManager.positions[tabKey] || 0

  // 如果滚动位置为0且不强制恢复，则跳过
  if (savedScrollTop === 0 && !force) {
    return
  }

  wx.pageScrollTo({
    scrollTop: savedScrollTop,
    duration,
  })

  console.log(`恢复滚动位置 - Tab: ${tabKey}, 位置: ${savedScrollTop}`)
}

/**
 * 获取指定tab的滚动位置
 * @param {Object} pageInstance 页面实例
 * @param {string|number} tabKey tab标识
 * @returns {number} 滚动位置
 */
function getScrollPosition(pageInstance, tabKey = "page") {
  if (!pageInstance.scrollPositionManager) {
    return 0
  }
  return pageInstance.scrollPositionManager.positions[tabKey] || 0
}

/**
 * 清除指定tab的滚动位置
 * @param {Object} pageInstance 页面实例
 * @param {string|number} tabKey tab标识，不传则清除所有
 */
function clearScrollPosition(pageInstance, tabKey = null) {
  if (!pageInstance.scrollPositionManager) {
    return
  }

  if (tabKey === null) {
    // 清除所有滚动位置
    Object.keys(pageInstance.scrollPositionManager.positions).forEach((key) => {
      pageInstance.scrollPositionManager.positions[key] = 0
    })
  } else {
    // 清除指定tab的滚动位置
    pageInstance.scrollPositionManager.positions[tabKey] = 0
  }

  // 更新缓存
  if (pageInstance.scrollPositionManager.enableCache) {
    saveScrollPositionsToCache(pageInstance)
  }
}

/**
 * 保存滚动位置到缓存
 * @param {Object} pageInstance 页面实例
 */
function saveScrollPositionsToCache(pageInstance) {
  if (!pageInstance.scrollPositionManager) {
    return
  }

  const { pageKey, positions, cacheExpireTime } =
    pageInstance.scrollPositionManager
  const cacheKey = `${CACHE_KEY_PREFIX}.${pageKey}`

  const cacheData = {
    positions,
    timestamp: Date.now(),
    expireTime: cacheExpireTime,
  }

  setCache(cacheKey, cacheData)
}

/**
 * 从缓存恢复滚动位置
 * @param {Object} pageInstance 页面实例
 */
function restoreScrollPositionsFromCache(pageInstance) {
  if (!pageInstance.scrollPositionManager) {
    return
  }

  const { pageKey } = pageInstance.scrollPositionManager
  const cacheKey = `${CACHE_KEY_PREFIX}.${pageKey}`
  const cacheData = getCache(cacheKey)

  if (!cacheData || !cacheData.positions) {
    return
  }

  // 检查缓存是否过期
  const now = Date.now()
  if (cacheData.timestamp && cacheData.expireTime) {
    if (now - cacheData.timestamp > cacheData.expireTime) {
      console.log(`滚动位置缓存已过期 - 页面: ${pageKey}`)
      return
    }
  }

  // 恢复滚动位置数据
  pageInstance.scrollPositionManager.positions = {
    ...pageInstance.scrollPositionManager.positions,
    ...cacheData.positions,
  }

  console.log(`从缓存恢复滚动位置 - 页面: ${pageKey}`, cacheData.positions)
}

/**
 * 清除页面的滚动位置缓存
 * @param {string} pageKey 页面标识
 */
function clearScrollPositionCache(pageKey) {
  const cacheKey = `${CACHE_KEY_PREFIX}.${pageKey}`
  setCache(cacheKey, null)
}

/**
 * 快速迁移工具 - 帮助现有页面快速迁移到新的滚动位置服务
 * @param {Object} pageInstance 页面实例
 * @param {string} pageKey 页面标识
 * @param {Object} existingTabScrollPositions 现有的 tabScrollPositions 对象
 */
function migrateFromTabScrollPositions(
  pageInstance,
  pageKey,
  existingTabScrollPositions = {}
) {
  // 初始化新的滚动位置管理器
  initScrollPositionManager(pageInstance, pageKey, {
    tabs: existingTabScrollPositions,
    enableCache: true,
  })

  // 迁移现有的滚动位置数据
  if (
    existingTabScrollPositions &&
    typeof existingTabScrollPositions === "object"
  ) {
    Object.keys(existingTabScrollPositions).forEach((tabKey) => {
      const scrollTop = existingTabScrollPositions[tabKey]
      if (typeof scrollTop === "number" && scrollTop > 0) {
        recordScrollPosition(pageInstance, scrollTop, tabKey)
      }
    })
  }

  console.log(`滚动位置数据迁移完成 - 页面: ${pageKey}`)
}

/**
 * 兼容性包装器 - 提供与现有代码兼容的接口
 * @param {Object} pageInstance 页面实例
 * @param {string} pageKey 页面标识
 * @returns {Object} 兼容性接口对象
 */
function createCompatibilityWrapper(pageInstance, pageKey) {
  // 初始化滚动位置管理器
  initScrollPositionManager(pageInstance, pageKey)

  // 返回兼容性接口
  return {
    // 兼容原有的 tabScrollPositions 对象
    get tabScrollPositions() {
      return new Proxy(
        {},
        {
          get(target, prop) {
            return getScrollPosition(pageInstance, prop)
          },
          set(target, prop, value) {
            recordScrollPosition(pageInstance, value, prop)
            return true
          },
        }
      )
    },

    // 兼容原有的 restoreTabScrollPosition 方法
    restoreTabScrollPosition(tabIndex) {
      restoreScrollPosition(pageInstance, tabIndex)
    },

    // 提供新的方法
    recordScroll(scrollTop, tabKey = "page") {
      recordScrollPosition(pageInstance, scrollTop, tabKey)
    },

    restoreScroll(tabKey = "page") {
      restoreScrollPosition(pageInstance, tabKey)
    },

    clearScroll(tabKey = null) {
      clearScrollPosition(pageInstance, tabKey)
    },
  }
}

module.exports = {
  initScrollPositionManager,
  recordScrollPosition,
  restoreScrollPosition,
  getScrollPosition,
  clearScrollPosition,
  saveScrollPositionsToCache,
  restoreScrollPositionsFromCache,
  clearScrollPositionCache,
  migrateFromTabScrollPositions,
  createCompatibilityWrapper,
}
